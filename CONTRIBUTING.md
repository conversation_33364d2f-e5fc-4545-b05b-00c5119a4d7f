# Contributing to AutoGen VS Code Extension

Thank you for your interest in contributing to the AutoGen VS Code Extension! This document provides guidelines and information for contributors.

## 🚀 Getting Started

### Prerequisites
- **Node.js**: Version 16 or higher
- **VS Code**: Version 1.74.0 or higher
- **Python**: Version 3.8 or higher (for AutoGen)
- **Git**: Latest version

### Development Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/autogen-team/vscode-autogen-extension.git
   cd vscode-autogen-extension
   ```

2. **Install dependencies**
   ```bash
   # Install extension dependencies
   npm install
   
   # Install WebView dependencies
   cd webview-ui
   npm install
   cd ..
   ```

3. **Build the project**
   ```bash
   npm run compile
   npm run build:webview
   ```

4. **Open in VS Code**
   ```bash
   code .
   ```

5. **Start debugging**
   - Press `F5` to launch the Extension Development Host
   - Or use the "Run Extension" configuration in the Debug panel

## 🏗️ Project Structure

```
vscode-autogen-extension/
├── src/                    # Extension source code
│   ├── extension.ts        # Main entry point
│   ├── webview/           # WebView management
│   ├── autogen/           # AutoGen integration
│   ├── commands/          # VS Code commands
│   ├── providers/         # VS Code providers
│   └── utils/             # Utility functions
├── webview-ui/            # React frontend
│   ├── src/               # React source code
│   ├── public/            # Static assets
│   └── package.json       # Frontend dependencies
├── docs/                  # Documentation
├── test/                  # Test files
└── templates/             # Agent/workflow templates
```

## 🧪 Testing

### Running Tests
```bash
# Run all tests
npm test

# Run extension tests only
npm run test:extension

# Run WebView tests only
cd webview-ui && npm test
```

### Writing Tests
- Place extension tests in `test/suite/`
- Place WebView tests in `webview-ui/src/__tests__/`
- Follow the existing test patterns and naming conventions
- Aim for 90%+ code coverage

## 📝 Code Style

### TypeScript Guidelines
- Use TypeScript strict mode
- Provide explicit return types for functions
- Use meaningful variable and function names
- Follow the existing code patterns

### Formatting
- Use Prettier for code formatting
- Run `npm run lint` before committing
- Configure your editor to format on save

### Commit Messages
Follow the [Conventional Commits](https://www.conventionalcommits.org/) specification:

```
type(scope): description

feat(webview): add agent designer interface
fix(autogen): resolve workflow execution error
docs(readme): update installation instructions
test(commands): add unit tests for agent creation
```

Types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

## 🐛 Bug Reports

When reporting bugs, please include:

1. **Environment Information**
   - VS Code version
   - Extension version
   - Operating system
   - Node.js version

2. **Steps to Reproduce**
   - Clear, numbered steps
   - Expected vs actual behavior
   - Screenshots if applicable

3. **Error Messages**
   - Full error messages
   - Stack traces
   - Console logs

## ✨ Feature Requests

For feature requests:

1. **Check existing issues** to avoid duplicates
2. **Describe the use case** and problem you're solving
3. **Provide examples** of how the feature would work
4. **Consider implementation** complexity and alternatives

## 🔄 Pull Request Process

### Before Submitting
1. **Fork the repository** and create a feature branch
2. **Write tests** for your changes
3. **Update documentation** if needed
4. **Run the full test suite** and ensure it passes
5. **Follow the code style** guidelines

### Pull Request Guidelines
1. **Clear title and description** explaining the changes
2. **Reference related issues** using keywords (fixes #123)
3. **Include screenshots** for UI changes
4. **Keep changes focused** - one feature/fix per PR
5. **Update CHANGELOG.md** if applicable

### Review Process
1. **Automated checks** must pass (CI/CD, linting, tests)
2. **Code review** by maintainers
3. **Testing** in the Extension Development Host
4. **Documentation review** if applicable

## 🏷️ Release Process

### Version Numbering
We follow [Semantic Versioning](https://semver.org/):
- **MAJOR**: Breaking changes
- **MINOR**: New features (backward compatible)
- **PATCH**: Bug fixes (backward compatible)

### Release Checklist
- [ ] Update version in `package.json`
- [ ] Update `CHANGELOG.md`
- [ ] Create release notes
- [ ] Tag the release
- [ ] Publish to VS Code Marketplace

## 🤝 Community Guidelines

### Code of Conduct
- Be respectful and inclusive
- Welcome newcomers and help them learn
- Focus on constructive feedback
- Respect different opinions and approaches

### Communication Channels
- **GitHub Issues**: Bug reports and feature requests
- **GitHub Discussions**: General questions and ideas
- **Discord**: Real-time community chat
- **Email**: <EMAIL> (security issues)

## 📚 Resources

### Documentation
- [VS Code Extension API](https://code.visualstudio.com/api)
- [AutoGen Documentation](https://microsoft.github.io/autogen/)
- [React Documentation](https://react.dev/)
- [TypeScript Handbook](https://www.typescriptlang.org/docs/)

### Tools
- [VS Code Extension Generator](https://github.com/Microsoft/vscode-generator-code)
- [WebView UI Toolkit](https://github.com/microsoft/vscode-webview-ui-toolkit)
- [Extension Test Runner](https://github.com/microsoft/vscode-test)

## 🙏 Recognition

Contributors will be:
- Listed in the `CONTRIBUTORS.md` file
- Mentioned in release notes for significant contributions
- Invited to join the maintainer team for exceptional contributions

## 📄 License

By contributing, you agree that your contributions will be licensed under the MIT License.

---

Thank you for contributing to the AutoGen VS Code Extension! 🚀
