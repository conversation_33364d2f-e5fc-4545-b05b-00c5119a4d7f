<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" 
          content="default-src 'none'; 
                   script-src 'nonce-{{nonce}}' 'unsafe-eval'; 
                   style-src 'nonce-{{nonce}}' 'unsafe-inline'; 
                   img-src data: https: vscode-resource:; 
                   font-src https: vscode-resource:;
                   connect-src https:;">
    <title>AutoGen Workflow Designer</title>
    <style nonce="{{nonce}}">
        /* Base styles for VS Code WebView */
        body {
            margin: 0;
            padding: 0;
            font-family: var(--vscode-font-family);
            font-size: var(--vscode-font-size);
            font-weight: var(--vscode-font-weight);
            color: var(--vscode-foreground);
            background-color: var(--vscode-editor-background);
            overflow: hidden;
        }

        /* Loading spinner */
        .loading-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            flex-direction: column;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--vscode-progressBar-background);
            border-top: 4px solid var(--vscode-progressBar-foreground);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .loading-text {
            margin-top: 16px;
            color: var(--vscode-descriptionForeground);
        }

        /* Error state */
        .error-container {
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            flex-direction: column;
            padding: 20px;
            text-align: center;
        }

        .error-icon {
            font-size: 48px;
            color: var(--vscode-errorForeground);
            margin-bottom: 16px;
        }

        .error-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 8px;
            color: var(--vscode-errorForeground);
        }

        .error-message {
            color: var(--vscode-descriptionForeground);
            margin-bottom: 16px;
        }

        /* Hide loading when React app loads */
        #root:not(:empty) + .loading-container {
            display: none;
        }

        /* Ensure React app takes full height */
        #root {
            height: 100vh;
            width: 100vw;
        }
    </style>
</head>
<body>
    <!-- React app root -->
    <div id="root"></div>
    
    <!-- Loading state (shown until React app loads) -->
    <div class="loading-container">
        <div class="loading-spinner"></div>
        <div class="loading-text">Loading AutoGen Workflow Designer...</div>
    </div>

    <!-- Error fallback (shown if React fails to load) -->
    <div id="error-fallback" class="error-container" style="display: none;">
        <div class="error-icon">⚠️</div>
        <div class="error-title">Failed to Load</div>
        <div class="error-message">
            The AutoGen Workflow Designer failed to load. Please try refreshing the panel.
        </div>
        <vscode-button id="retry-button">Retry</vscode-button>
    </div>

    <!-- VS Code WebView API initialization -->
    <script nonce="{{nonce}}">
        // Initialize VS Code WebView API
        const vscode = acquireVsCodeApi();
        
        // Make vscode API globally available
        window.vscode = vscode;
        
        // Error handling for React app
        window.addEventListener('error', (event) => {
            console.error('WebView Error:', event.error);
            document.getElementById('root').style.display = 'none';
            document.querySelector('.loading-container').style.display = 'none';
            document.getElementById('error-fallback').style.display = 'flex';
        });

        // Retry functionality
        document.getElementById('retry-button')?.addEventListener('click', () => {
            window.location.reload();
        });

        // Message handling setup
        window.addEventListener('message', (event) => {
            const message = event.data;
            
            // Dispatch custom event for React app to handle
            window.dispatchEvent(new CustomEvent('vscode-message', {
                detail: message
            }));
        });

        // Development mode helpers
        if (typeof __DEV__ !== 'undefined' && __DEV__) {
            console.log('AutoGen WebView running in development mode');
            
            // Mock VS Code API for development
            if (!window.vscode) {
                window.vscode = {
                    postMessage: (message) => {
                        console.log('Mock VS Code message:', message);
                    },
                    getState: () => ({}),
                    setState: (state) => {
                        console.log('Mock VS Code state:', state);
                    }
                };
            }
        }
    </script>

    <!-- React app entry point -->
    <script type="module" src="/src/main.tsx" nonce="{{nonce}}"></script>
</body>
</html>
