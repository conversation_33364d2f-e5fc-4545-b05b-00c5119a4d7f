{"compilerOptions": {"module": "commonjs", "target": "ES2020", "outDir": "out", "lib": ["ES2020"], "sourceMap": true, "rootDir": "src", "strict": true, "moduleResolution": "node", "baseUrl": "./", "paths": {"@/*": ["src/*"], "@/webview/*": ["src/webview/*"], "@/autogen/*": ["src/autogen/*"], "@/commands/*": ["src/commands/*"], "@/providers/*": ["src/providers/*"], "@/utils/*": ["src/utils/*"]}, "esModuleInterop": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "declaration": true, "declarationMap": true, "experimentalDecorators": true, "emitDecoratorMetadata": true}, "include": ["src/**/*"], "exclude": ["node_modules", ".vscode-test", "out", "webview-ui"]}