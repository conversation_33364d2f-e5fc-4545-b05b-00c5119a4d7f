{"version": "2.0.0", "tasks": [{"type": "npm", "script": "watch", "problemMatcher": "$tsc-watch", "isBackground": true, "presentation": {"reveal": "never"}, "group": {"kind": "build", "isDefault": true}}, {"type": "npm", "script": "compile", "problemMatcher": "$tsc", "group": "build"}, {"type": "npm", "script": "test", "problemMatcher": "$tsc", "group": "test"}, {"type": "npm", "script": "build:webview", "problemMatcher": [], "group": "build", "label": "Build WebView"}, {"type": "shell", "command": "cd webview-ui && npm run dev", "label": "Start WebView Dev Server", "isBackground": true, "problemMatcher": {"pattern": {"regexp": "."}, "background": {"activeOnStart": true, "beginsPattern": ".", "endsPattern": "Local:.*"}}, "group": "build"}, {"type": "npm", "script": "package", "label": "Package Extension", "group": "build", "dependsOn": ["npm: compile", "Build WebView"]}]}