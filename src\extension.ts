import * as vscode from 'vscode';
import { WebViewManager } from './webview/WebViewManager';
import { AgentManager } from './autogen/AgentManager';
import { AutoGenTreeDataProvider } from './providers/TreeDataProvider';
import { Logger } from './utils/Logger';
import { CreateAgentCommand } from './commands/CreateAgent';
import { RunWorkflowCommand } from './commands/RunWorkflow';
import { ExportProjectCommand } from './commands/ExportProject';

/**
 * Main extension activation function
 * Called when the extension is activated
 */
export function activate(context: vscode.ExtensionContext) {
    Logger.info('AutoGen extension is being activated');

    try {
        // Initialize core managers
        const webViewManager = new WebViewManager(context);
        const agentManager = new AgentManager(context);
        const treeDataProvider = new AutoGenTreeDataProvider(context, agentManager);

        // Register tree view
        const treeView = vscode.window.createTreeView('autogenExplorer', {
            treeDataProvider: treeDataProvider,
            showCollapseAll: true,
            canSelectMany: false
        });

        // Register commands
        const commands = [
            vscode.commands.registerCommand('autogen.createAgent', () => 
                new CreateAgentCommand(webViewManager, agentManager).execute()
            ),
            vscode.commands.registerCommand('autogen.openWorkflow', () => 
                webViewManager.showWorkflowDesigner()
            ),
            vscode.commands.registerCommand('autogen.runWorkflow', (workflowItem) => 
                new RunWorkflowCommand(agentManager).execute(workflowItem)
            ),
            vscode.commands.registerCommand('autogen.stopWorkflow', (workflowItem) => 
                agentManager.stopWorkflow(workflowItem.id)
            ),
            vscode.commands.registerCommand('autogen.exportProject', () => 
                new ExportProjectCommand(agentManager).execute()
            ),
            vscode.commands.registerCommand('autogen.importProject', () => 
                agentManager.importProject()
            ),
            vscode.commands.registerCommand('autogen.refreshExplorer', () => 
                treeDataProvider.refresh()
            )
        ];

        // Register providers
        const providers = [
            // Tree view is already registered above
            treeView
        ];

        // Set up workspace context
        updateWorkspaceContext();

        // Watch for workspace changes
        const workspaceWatcher = vscode.workspace.onDidChangeWorkspaceFolders(() => {
            updateWorkspaceContext();
            treeDataProvider.refresh();
        });

        // Watch for file changes
        const fileWatcher = vscode.workspace.createFileSystemWatcher('**/*.autogen.json');
        fileWatcher.onDidCreate(() => treeDataProvider.refresh());
        fileWatcher.onDidChange(() => treeDataProvider.refresh());
        fileWatcher.onDidDelete(() => treeDataProvider.refresh());

        // Add all disposables to context
        context.subscriptions.push(
            ...commands,
            ...providers,
            workspaceWatcher,
            fileWatcher,
            webViewManager,
            agentManager
        );

        // Show welcome message for first-time users
        showWelcomeMessage(context);

        Logger.info('AutoGen extension activated successfully');

    } catch (error) {
        Logger.error('Failed to activate AutoGen extension', error);
        vscode.window.showErrorMessage(
            'Failed to activate AutoGen extension. Please check the logs for details.'
        );
    }
}

/**
 * Extension deactivation function
 * Called when the extension is deactivated
 */
export function deactivate() {
    Logger.info('AutoGen extension is being deactivated');
    // Cleanup is handled by disposables in context.subscriptions
}

/**
 * Updates workspace context variables
 */
function updateWorkspaceContext() {
    const hasAutoGenProject = checkForAutoGenProject();
    vscode.commands.executeCommand('setContext', 'workspaceHasAutoGenProject', hasAutoGenProject);
}

/**
 * Checks if the current workspace has AutoGen project files
 */
function checkForAutoGenProject(): boolean {
    if (!vscode.workspace.workspaceFolders) {
        return false;
    }

    // Check for .autogen.json files or autogen configuration
    return vscode.workspace.workspaceFolders.some(folder => {
        const pattern = new vscode.RelativePattern(folder, '**/*.autogen.json');
        return vscode.workspace.findFiles(pattern, null, 1).then(files => files.length > 0);
    });
}

/**
 * Shows welcome message for first-time users
 */
async function showWelcomeMessage(context: vscode.ExtensionContext) {
    const hasShownWelcome = context.globalState.get('autogen.hasShownWelcome', false);
    
    if (!hasShownWelcome) {
        const action = await vscode.window.showInformationMessage(
            'Welcome to AutoGen! Would you like to create your first agent?',
            'Create Agent',
            'Open Documentation',
            'Later'
        );

        switch (action) {
            case 'Create Agent':
                vscode.commands.executeCommand('autogen.createAgent');
                break;
            case 'Open Documentation':
                vscode.env.openExternal(vscode.Uri.parse('https://github.com/autogen-team/vscode-autogen-extension#readme'));
                break;
        }

        context.globalState.update('autogen.hasShownWelcome', true);
    }
}
