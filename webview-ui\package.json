{"name": "autogen-webview-ui", "private": true, "version": "0.1.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "type-check": "tsc --noEmit"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "@vscode/webview-ui-toolkit": "^1.2.2", "@monaco-editor/react": "^4.6.0", "d3": "^7.8.5", "vis-network": "^9.1.6", "uuid": "^9.0.0", "ajv": "^8.12.0", "lodash": "^4.17.21"}, "devDependencies": {"@types/react": "^18.2.43", "@types/react-dom": "^18.2.17", "@types/d3": "^7.4.3", "@types/uuid": "^9.0.7", "@types/lodash": "^4.14.202", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.55.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "typescript": "^5.2.2", "vite": "^5.0.8"}}