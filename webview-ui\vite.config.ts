import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  build: {
    outDir: '../out/webview',
    emptyOutDir: true,
    rollupOptions: {
      input: {
        main: resolve(__dirname, 'index.html'),
      },
      output: {
        entryFileNames: 'assets/[name].js',
        chunkFileNames: 'assets/[name].js',
        assetFileNames: 'assets/[name].[ext]'
      }
    },
    // Optimize for VS Code WebView
    target: 'es2020',
    minify: 'esbuild',
    sourcemap: true,
    // Ensure compatibility with VS Code's CSP
    cssCodeSplit: false
  },
  define: {
    // Define global constants for the WebView environment
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
    __VSCODE_WEBVIEW__: JSON.stringify(true)
  },
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@components': resolve(__dirname, 'src/components'),
      '@hooks': resolve(__dirname, 'src/hooks'),
      '@services': resolve(__dirname, 'src/services'),
      '@types': resolve(__dirname, 'src/types'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@styles': resolve(__dirname, 'src/styles')
    }
  },
  server: {
    port: 3000,
    host: 'localhost',
    // Configure for development with VS Code extension
    cors: true,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization'
    }
  },
  // Optimize dependencies for WebView
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      '@vscode/webview-ui-toolkit',
      'uuid',
      'lodash'
    ],
    exclude: [
      // Exclude Node.js specific modules
      'fs',
      'path',
      'os'
    ]
  }
});
