{"name": "vscode-autogen-extension", "displayName": "AutoGen Multi-Agent Workflows", "description": "Build, test, and deploy AI agent systems with AutoGen directly in VS Code", "version": "0.1.0", "publisher": "autogen-team", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Machine Learning", "Snippets", "Debuggers"], "keywords": ["autogen", "ai", "agents", "multi-agent", "workflow", "automation", "llm", "chatgpt"], "activationEvents": ["onCommand:autogen.createAgent", "onCommand:autogen.openWorkflow", "onView:autogenExplorer", "onLanguage:json", "workspaceContains:**/*.autogen.json"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "autogen.createAgent", "title": "Create New Agent", "category": "AutoGen", "icon": "$(add)"}, {"command": "autogen.openWorkflow", "title": "Open Workflow Designer", "category": "AutoGen", "icon": "$(graph)"}, {"command": "autogen.runWorkflow", "title": "Run Workflow", "category": "AutoGen", "icon": "$(play)"}, {"command": "autogen.stopWorkflow", "title": "Stop Workflow", "category": "AutoGen", "icon": "$(stop)"}, {"command": "autogen.exportProject", "title": "Export Project", "category": "AutoGen", "icon": "$(export)"}, {"command": "autogen.importProject", "title": "Import Project", "category": "AutoGen", "icon": "$(import)"}, {"command": "autogen.refreshExplorer", "title": "Refresh", "category": "AutoGen", "icon": "$(refresh)"}], "views": {"explorer": [{"id": "autogenExplorer", "name": "AutoGen Agents", "when": "workspaceHasAutoGenProject"}]}, "viewsContainers": {"activitybar": [{"id": "autogen", "title": "AutoGen", "icon": "$(robot)"}]}, "menus": {"view/title": [{"command": "autogen.createAgent", "when": "view == autogenExplorer", "group": "navigation"}, {"command": "autogen.refreshExplorer", "when": "view == autogenExplorer", "group": "navigation"}], "view/item/context": [{"command": "autogen.runWorkflow", "when": "view == autogenExplorer && viewItem == workflow", "group": "inline"}, {"command": "autogen.stopWorkflow", "when": "view == autogenExplorer && viewItem == runningWorkflow", "group": "inline"}], "commandPalette": [{"command": "autogen.createAgent", "when": "workspaceHasAutoGenProject"}, {"command": "autogen.openWorkflow", "when": "workspaceHasAutoGenProject"}]}, "configuration": {"title": "AutoGen", "properties": {"autogen.pythonPath": {"type": "string", "default": "python", "description": "Path to Python executable for AutoGen"}, "autogen.autoSave": {"type": "boolean", "default": true, "description": "Automatically save agent configurations"}, "autogen.logLevel": {"type": "string", "enum": ["debug", "info", "warn", "error"], "default": "info", "description": "Logging level for AutoGen operations"}, "autogen.maxAgents": {"type": "number", "default": 10, "description": "Maximum number of agents per workflow"}, "autogen.defaultModel": {"type": "string", "default": "gpt-4", "description": "Default LLM model for new agents"}}}, "jsonValidation": [{"fileMatch": "*.autogen.json", "url": "./schemas/autogen-config.schema.json"}], "languages": [{"id": "autogen-config", "aliases": ["AutoGen Configuration", "autogen"], "extensions": [".autogen.json"], "configuration": "./language-configuration.json"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./", "pretest": "npm run compile && npm run lint", "lint": "eslint src --ext ts", "test": "node ./out/test/runTest.js", "build:webview": "cd webview-ui && npm run build", "dev:webview": "cd webview-ui && npm run dev", "package": "vsce package", "deploy": "vsce publish"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "eslint": "^8.28.0", "typescript": "^4.9.4", "@vscode/test-electron": "^2.2.0", "@vscode/vsce": "^2.15.0"}, "dependencies": {"uuid": "^9.0.0", "ajv": "^8.12.0", "ws": "^8.13.0"}, "repository": {"type": "git", "url": "https://github.com/autogen-team/vscode-autogen-extension.git"}, "bugs": {"url": "https://github.com/autogen-team/vscode-autogen-extension/issues"}, "homepage": "https://github.com/autogen-team/vscode-autogen-extension#readme", "license": "MIT", "icon": "media/icon.png", "galleryBanner": {"color": "#1e1e1e", "theme": "dark"}}